<?php
class WSL_Scheduler {
    public static function activate() {
        $frequency = get_option('wsl_check_frequency', 'hourly');
        if (!wp_next_scheduled('wsl_check_inactive_users')) {
            wp_schedule_event(time(), $frequency, 'wsl_check_inactive_users');
        }
    }

    public static function deactivate() {
        wp_clear_scheduled_hook('wsl_check_inactive_users');
    }

    public static function update_schedule() {
        // Καθαρισμός παλιού προγραμματισμού
        wp_clear_scheduled_hook('wsl_check_inactive_users');

        // Δημιουργία νέου με την ενημερωμένη συχνότητα
        $frequency = get_option('wsl_check_frequency', 'hourly');
        wp_schedule_event(time(), $frequency, 'wsl_check_inactive_users');

        // Ενημέρωση του χρόνου τελευταίας αλλαγής
        update_option('wsl_schedule_last_updated', time());
    }

    /**
     * Επαναπρογραμματισμός όταν αλλάζει η συχνότητα
     */
    public static function reschedule_on_frequency_change($new_frequency) {
        // Καθαρισμός παλιών προγραμματισμένων εργασιών
        wp_clear_scheduled_hook('wsl_check_inactive_users');

        // Προγραμματισμός με νέα συχνότητα
        wp_schedule_event(time(), $new_frequency, 'wsl_check_inactive_users');

        // Ενημέρωση options
        update_option('wsl_check_frequency', $new_frequency);
        update_option('wsl_schedule_last_updated', time());

        return true;
    }

    public static function get_next_run_info() {
        $next_run = wp_next_scheduled('wsl_check_inactive_users');
        $frequency = get_option('wsl_check_frequency', 'hourly');
        $last_run = get_option('wsl_last_run_time', false);

        // Υπολογισμός διαστήματος σε δευτερόλεπτα
        $intervals = [
            'hourly' => 3600,
            'every_2_hours' => 7200, // 2 ώρες
            'every_4_hours' => 14400, // 4 ώρες
            'twicedaily' => 43200, // 12 ώρες
            'daily' => 86400,
            'weekly' => 604800
        ];

        $interval_seconds = isset($intervals[$frequency]) ? $intervals[$frequency] : 3600;

        // Αν δεν υπάρχει προγραμματισμένος έλεγχος, υπολόγισε τον επόμενο
        if (!$next_run) {
            if ($last_run) {
                $next_run = $last_run + $interval_seconds;
            } else {
                $next_run = time() + $interval_seconds;
            }
        }

        return [
            'next_run' => $next_run,
            'frequency' => $frequency,
            'interval_seconds' => $interval_seconds,
            'last_run' => $last_run
        ];
    }

    /**
     * Υπολογίζει πότε θα πρέπει να γίνει ο επόμενος έλεγχος βάσει συχνότητας
     */
    public static function calculate_next_check_time() {
        $frequency = get_option('wsl_check_frequency', 'hourly');
        $last_run = get_option('wsl_last_run_time', false);

        $intervals = [
            'hourly' => 3600,
            'every_2_hours' => 7200,
            'every_4_hours' => 14400,
            'twicedaily' => 43200,
            'daily' => 86400,
            'weekly' => 604800
        ];

        $interval_seconds = isset($intervals[$frequency]) ? $intervals[$frequency] : 3600;

        if ($last_run) {
            return $last_run + $interval_seconds;
        } else {
            return time() + $interval_seconds;
        }
    }
}

add_action('wsl_check_inactive_users', 'wsl_execute_user_check');

function wsl_execute_user_check() {
    WSL_Cleaner::remove_inactive_users();
}
