<?php

/**
 * Λήψη ανενεργών χρηστών που δεν έχουν συνδεθεί ποτέ
 */
function wsl_get_inactive_users() {
    $frequency = get_option('wsl_check_frequency', 'hourly');
    $frequency_intervals = [
        'hourly' => 3600,        // 1 ώρα
        'every_2_hours' => 7200, // 2 ώρες
        'every_4_hours' => 14400,// 4 ώρες
        'twicedaily' => 43200,   // 12 ώρες
        'daily' => 86400,        // 24 ώρες
        'weekly' => 604800       // 7 ημέρες
    ];
    $timeout = isset($frequency_intervals[$frequency]) ? $frequency_intervals[$frequency] : 3600;
    $now = current_time('timestamp');
    $cutoff_time = $now - $timeout;

    // Λήψη εξαιρούμενων ρόλων από τις ρυθμίσεις
    $excluded_roles = get_option('wsl_exclude_roles', ['administrator']);

    $users = get_users([
        'role__not_in' => $excluded_roles,  // ΠΡΟΣΘΗΚΗ: Εξαίρεση ρόλων
        'meta_query' => [
            'relation' => 'OR',
            [
                'key' => 'last_login',
                'compare' => 'NOT EXISTS'
            ],
            [
                'key' => 'last_login',
                'value' => '',
                'compare' => '='
            ]
        ],
        'date_query' => [
            [
                'before' => date('Y-m-d H:i:s', $cutoff_time),
                'inclusive' => true,
            ],
        ],
        'orderby' => 'user_registered',
        'order' => 'ASC'
    ]);

    $inactive_users = [];
    foreach ($users as $user) {
        // Επιπλέον έλεγχος ρόλων για διπλή ασφάλεια
        $user_data = get_userdata($user->ID);
        $user_roles = $user_data->roles;
        $has_excluded_role = array_intersect($user_roles, $excluded_roles);

        if (!empty($has_excluded_role)) {
            continue; // Παράλειψη χρήστη με εξαιρούμενο ρόλο
        }

        $registered_time = strtotime($user->user_registered);
        $time_since_registration = $now - $registered_time;

        // Έλεγχος αν ο χρήστης είναι υποψήφιος για διαγραφή
        if ($time_since_registration >= $timeout) {
            $inactive_users[] = [
                'user_id' => $user->ID,
                'username' => $user->user_login,
                'display_name' => $user->display_name,
                'email' => $user->user_email,
                'roles' => implode(', ', $user_roles),
                'registered_date' => $user->user_registered,
                'last_login' => get_user_meta($user->ID, 'last_login', true) ?: 'Ποτέ',
                'time_until_deletion' => max(0, $timeout - $time_since_registration),
                'will_be_deleted' => $time_since_registration >= $timeout
            ];
        }
    }

    return $inactive_users;
}

/**
 * Φιλτράρισμα ανενεργών χρηστών
 */
function wsl_get_filtered_inactive_users($filters) {
    $all_users = wsl_get_inactive_users();
    $filtered = [];

    foreach ($all_users as $user) {
        $include = true;

        // Φίλτρο username
        if (!empty($filters['username']) && stripos($user['username'], $filters['username']) === false) {
            $include = false;
        }

        // Φίλτρο email
        if (!empty($filters['email']) && stripos($user['email'], $filters['email']) === false) {
            $include = false;
        }

        // Φίλτρο role
        if (!empty($filters['role']) && stripos($user['roles'], $filters['role']) === false) {
            $include = false;
        }

        // Φίλτρο user_id
        if (!empty($filters['user_id']) && $user['user_id'] != $filters['user_id']) {
            $include = false;
        }

        // Φίλτρο ημερομηνίας
        if (!empty($filters['date_from'])) {
            $date_from = strtotime($filters['date_from']);
            $user_date = strtotime($user['registered_date']);
            if ($user_date < $date_from) {
                $include = false;
            }
        }

        if (!empty($filters['date_to'])) {
            $date_to = strtotime($filters['date_to'] . ' 23:59:59');
            $user_date = strtotime($user['registered_date']);
            if ($user_date > $date_to) {
                $include = false;
            }
        }

        if ($include) {
            $filtered[] = $user;
        }
    }

    return $filtered;
}

add_action('admin_menu', function() {
    add_submenu_page(
        'users.php',
        'WSL Ανενεργοί Χρήστες',
        'WSL Ανενεργοί Χρήστες',
        'manage_options',
        'wsl-remove-inactive-users',
        'wsl_admin_page'
    );
});

// Προσθήκη CSS και JS για τα tabs
add_action('admin_enqueue_scripts', function($hook) {
    // Φόρτωση μόνο στη σελίδα του plugin
    if ($hook !== 'users_page_wsl-remove-inactive-users') {
        return;
    }

    // Φόρτωση Chart.js στο footer για σωστή σειρά φόρτωσης
    wp_enqueue_script('chart-js', 'https://cdn.jsdelivr.net/npm/chart.js', [], '3.9.1', true);
});

add_action('admin_head', function() {
    $screen = get_current_screen();
    if ($screen && $screen->id === 'users_page_wsl-remove-inactive-users') {
        ?>
        <style>
        .nav-tab-wrapper { margin-bottom: 20px; }
        .wsl-tab-content { display: none; }
        .wsl-tab-content.active { display: block; }
        .wsl-stats { background: #f1f1f1; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .wsl-stats .stat-item { display: inline-block; margin-right: 30px; }
        .wsl-stats .stat-number { font-size: 24px; font-weight: bold; color: #0073aa; }
        .wsl-clear-logs { color: #a00; text-decoration: none; }
        .wsl-clear-logs:hover { color: #dc3232; }
        .wsl-countdown {
            display: inline-block;
        }
        .wsl-countdown-timer {
            font-size: 16px;
            font-weight: bold;
            color: #0073aa;
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
        }
        .wsl-old-log {
            background-color: #fff3cd;
            color: #856404;
        }
        .wsl-enhanced-log {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .wsl-deleted-user {
            background-color: #f8d7da !important;
            color: #721c24 !important;
        }
        .wsl-existing-user {
            background-color: #d4edda;
            color: #155724;
        }
        .wsl-safe-user {
            background-color: #d4edda !important;
            color: #155724 !important;
        }
        .wsl-warning-user {
            background-color: #fff3cd !important;
            color: #856404 !important;
        }

        /* Παρακάμπτουμε το WordPress striped table styling */
        .widefat .wsl-deleted-user,
        .widefat .wsl-deleted-user:nth-child(odd),
        .widefat .wsl-deleted-user:nth-child(even) {
            background-color: #f8d7da !important;
            color: #721c24 !important;
        }

        .widefat .wsl-warning-user,
        .widefat .wsl-warning-user:nth-child(odd),
        .widefat .wsl-warning-user:nth-child(even) {
            background-color: #fff3cd !important;
            color: #856404 !important;
        }

        .widefat .wsl-safe-user,
        .widefat .wsl-safe-user:nth-child(odd),
        .widefat .wsl-safe-user:nth-child(even) {
            background-color: #d4edda !important;
            color: #155724 !important;
        }

        /* Προστατευμένοι χρήστες */
        .wsl-protected-user {
            background-color: #d1ecf1 !important;
            color: #0c5460 !important;
        }

        .widefat .wsl-protected-user,
        .widefat .wsl-protected-user:nth-child(odd),
        .widefat .wsl-protected-user:nth-child(even) {
            background-color: #d1ecf1 !important;
            color: #0c5460 !important;
        }

        /* Protect button styling */
        .wsl-protect-user-btn {
            transition: all 0.3s ease;
        }

        .wsl-protect-user-btn:hover {
            transform: scale(1.2);
            color: #005177 !important;
        }

        .wsl-protect-user-btn:disabled {
            opacity: 0.8;
            cursor: not-allowed;
        }

        /* Smooth row transitions */
        .wsl-user-row {
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        /* Compact notices */
        .notice.is-dismissible {
            border-left-width: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .notice.notice-success {
            border-left-color: #46b450;
        }

        .notice.notice-error {
            border-left-color: #dc3232;
        }
        .wsl-old-log-border {
            border-left: 4px solid #ffc107;
        }
        .wsl-enhanced-log-border {
            border-left: 4px solid #17a2b8;
        }
        .wsl-filters {
            background: #f9f9f9;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .wsl-filter-row {
            display: flex;
            gap: 10px;
            margin-bottom: 8px;
            align-items: center;
            flex-wrap: wrap;
        }
        .wsl-filter-row label {
            min-width: 80px;
            font-weight: bold;
            font-size: 13px;
        }
        .wsl-filter-row input[type="text"],
        .wsl-filter-row input[type="date"],
        .wsl-filter-row input[type="number"] {
            max-width: 150px;
        }
        .wsl-export-buttons {
            margin: 10px 0;
        }
        .wsl-export-buttons a {
            margin-right: 10px;
        }
        .wsl-accordion {
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 15px;
            background: #fff;
        }
        .wsl-accordion-header {
            background: #f1f1f1;
            padding: 12px 15px;
            cursor: pointer;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }
        .wsl-accordion-header:hover {
            background: #e8e8e8;
        }
        .wsl-accordion-content {
            display: none;
            padding: 15px;
            background: #f9f9f9;
            overflow: hidden;
        }
        .wsl-accordion-toggle {
            font-size: 14px;
            color: #666;
            font-family: monospace;
            font-weight: bold;
            pointer-events: none;
        }
        .wsl-chart-container {
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .wsl-chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .wsl-summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .wsl-summary-stat {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border-left: 4px solid #0073aa;
        }
        .wsl-summary-stat .number {
            font-size: 24px;
            font-weight: bold;
            color: #0073aa;
        }
        .wsl-summary-stat .label {
            font-size: 14px;
            color: #666;
        }
        </style>
        <script>
        jQuery(document).ready(function($) {
            // Comprehensive error handler για JavaScript errors
            window.addEventListener('error', function(e) {
                // Suppress common non-critical errors
                var suppressedErrors = [
                    'Chart is not defined',
                    'Email input element not found',
                    'Cannot read property',
                    'Cannot read properties of null',
                    'Cannot read properties of undefined',
                    'getElementById',
                    'querySelector'
                ];

                var shouldSuppress = suppressedErrors.some(function(errorText) {
                    return e.message && e.message.includes(errorText);
                });

                if (shouldSuppress) {
                    console.warn('WSL Plugin: Suppressed non-critical error:', e.message);
                    return true; // Suppress error
                }
            });

            // Additional error handling for unhandled promise rejections
            window.addEventListener('unhandledrejection', function(e) {
                console.warn('WSL Plugin: Unhandled promise rejection:', e.reason);
                e.preventDefault(); // Prevent the default handling
            });

            $('.nav-tab').click(function(e) {
                e.preventDefault();
                $('.nav-tab').removeClass('nav-tab-active');
                $('.wsl-tab-content').removeClass('active');
                $(this).addClass('nav-tab-active');
                $($(this).attr('href')).addClass('active');
            });

            $('.wsl-clear-logs').click(function(e) {
                if (!confirm('Είστε σίγουροι ότι θέλετε να διαγράψετε όλα τα logs;')) {
                    e.preventDefault();
                }
            });

            // Countdown Timer για τις ρυθμίσεις
            var countdownStartTime = null;
            var countdownDuration = null;

            function updateCountdown() {
                var $element = $('#wsl-next-run-timestamp');
                var frequency = $element.data('frequency') || $('select[name="wsl_check_frequency"]').val() || 'hourly';

                var intervals = {
                    'hourly': 3600,
                    'every_2_hours': 7200,
                    'every_4_hours': 14400,
                    'twicedaily': 43200,
                    'daily': 86400,
                    'weekly': 604800
                };

                var interval = intervals[frequency] || 3600;
                var now = Math.floor(Date.now() / 1000);

                // Αρχικοποίηση countdown αν δεν έχει γίνει
                if (countdownStartTime === null) {
                    // Πάντα ξεκινάμε από το interval της συχνότητας για συνέπεια
                    countdownDuration = interval;
                    countdownStartTime = Date.now();
                    console.log('Countdown initialized with interval:', interval + 's for frequency:', frequency);
                }

                // Υπολογισμός χρόνου που έχει περάσει
                var elapsed = Math.floor((Date.now() - countdownStartTime) / 1000);
                var timeLeft = Math.max(0, countdownDuration - elapsed);

                // Reset αν ο χρόνος τελείωσε
                if (timeLeft <= 0) {
                    countdownStartTime = Date.now();
                    countdownDuration = interval;
                    timeLeft = interval;
                }

                var hours = Math.floor(timeLeft / 3600);
                var minutes = Math.floor((timeLeft % 3600) / 60);
                var seconds = timeLeft % 60;
                var milliseconds = Math.floor((Date.now() - countdownStartTime) % 1000);

                var timeString =
                    String(hours).padStart(2, '0') + ':' +
                    String(minutes).padStart(2, '0') + ':' +
                    String(seconds).padStart(2, '0') + ':' +
                    String(milliseconds).padStart(3, '0');

                $('#wsl-countdown-timer').html(timeString);
            }

            // Countdown Timer για το logs tab
            var countdownStartTimeLogs = null;
            var countdownDurationLogs = null;

            function updateCountdownLogs() {
                var $element = $('#wsl-next-run-timestamp-logs');
                var frequency = $element.data('frequency') || $('select[name="wsl_check_frequency"]').val() || 'hourly';

                var intervals = {
                    'hourly': 3600,
                    'every_2_hours': 7200,
                    'every_4_hours': 14400,
                    'twicedaily': 43200,
                    'daily': 86400,
                    'weekly': 604800
                };

                var interval = intervals[frequency] || 3600;
                var now = Math.floor(Date.now() / 1000);

                // Αρχικοποίηση countdown αν δεν έχει γίνει
                if (countdownStartTimeLogs === null) {
                    // Πάντα ξεκινάμε από το interval της συχνότητας για συνέπεια
                    countdownDurationLogs = interval;
                    countdownStartTimeLogs = Date.now();
                    console.log('Countdown Logs initialized with interval:', interval + 's for frequency:', frequency);
                }

                // Υπολογισμός χρόνου που έχει περάσει
                var elapsed = Math.floor((Date.now() - countdownStartTimeLogs) / 1000);
                var timeLeft = Math.max(0, countdownDurationLogs - elapsed);

                // Reset αν ο χρόνος τελείωσε
                if (timeLeft <= 0) {
                    countdownStartTimeLogs = Date.now();
                    countdownDurationLogs = interval;
                    timeLeft = interval;
                }

                var hours = Math.floor(timeLeft / 3600);
                var minutes = Math.floor((timeLeft % 3600) / 60);
                var seconds = timeLeft % 60;
                var milliseconds = Math.floor((Date.now() - countdownStartTimeLogs) % 1000);

                var timeString =
                    String(hours).padStart(2, '0') + ':' +
                    String(minutes).padStart(2, '0') + ':' +
                    String(seconds).padStart(2, '0') + ':' +
                    String(milliseconds).padStart(3, '0');

                $('#wsl-countdown-timer-logs').html(timeString);
            }

            // Function για ενημέρωση countdown βάσει νέας συχνότητας
            function refreshCountdownWithNewFrequency() {
                var frequencySelect = $('select[name="wsl_check_frequency"]');
                if (frequencySelect.length) {
                    var newFrequency = frequencySelect.val();
                    var intervals = {
                        'hourly': 3600,
                        'every_2_hours': 7200,
                        'every_4_hours': 14400,
                        'twicedaily': 43200,
                        'daily': 86400,
                        'weekly': 604800
                    };

                    var newInterval = intervals[newFrequency] || 3600;

                    // Reset countdown timers με νέα συχνότητα
                    countdownStartTime = Date.now();
                    countdownDuration = newInterval;
                    countdownStartTimeLogs = Date.now();
                    countdownDurationLogs = newInterval;

                    // Ενημέρωση data attributes
                    $('#wsl-next-run-timestamp').data('frequency', newFrequency);
                    $('#wsl-next-run-timestamp-logs').data('frequency', newFrequency);

                    console.log('Countdown reset for frequency:', newFrequency, 'interval:', newInterval + 's');
                }
            }

            // Function για λήψη πραγματικού next scheduled time από server
            function fetchNextScheduledTime() {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'wsl_get_next_scheduled',
                        nonce: '<?php echo wp_create_nonce('wsl_get_next_scheduled'); ?>'
                    },
                    success: function(response) {
                        if (response.success && response.data.next_scheduled) {
                            var nextScheduled = response.data.next_scheduled;

                            // Αποθήκευση του scheduled time χωρίς να επηρεάζουμε το countdown
                            $('#wsl-next-run-timestamp').data('next-scheduled', nextScheduled);
                            $('#wsl-next-run-timestamp-logs').data('next-scheduled', nextScheduled);

                            console.log('Next scheduled time updated:', new Date(nextScheduled * 1000));
                        }
                    }
                });
            }

            // Παρακολούθηση αλλαγών στη συχνότητα
            $(document).on('change', 'select[name="wsl_check_frequency"]', function() {
                var newFrequency = $(this).val();
                var frequencyLabels = {
                    'hourly': 'Ωριαία',
                    'every_2_hours': 'Κάθε 2 ώρες',
                    'every_4_hours': 'Κάθε 4 ώρες',
                    'twicedaily': 'Δύο φορές την ημέρα',
                    'daily': 'Ημερησίως',
                    'weekly': 'Εβδομαδιαία'
                };

                // Άμεση ενημέρωση countdown
                refreshCountdownWithNewFrequency();

                // Λήψη νέου scheduled time από server
                fetchNextScheduledTime();

                // Εμφάνιση μηνύματος ότι ενημερώθηκε
                var frequencyLabel = frequencyLabels[newFrequency] || newFrequency;
                var $notice = $('<div class="notice notice-info is-dismissible" style="margin: 10px 0;"><p>⏰ Countdown timer ενημερώθηκε για συχνότητα: <strong>' + frequencyLabel + '</strong></p></div>');
                $('.wrap h1').after($notice);

                // Αφαίρεση μηνύματος μετά από 4 δευτερόλεπτα
                setTimeout(function() {
                    $notice.fadeOut(500, function() {
                        $notice.remove();
                    });
                }, 4000);
            });

            // Αρχική φόρτωση του next scheduled time
            fetchNextScheduledTime();

            // Refresh Statistics functionality
            $('#wsl-refresh-stats').on('click', function() {
                var button = $(this);
                var originalText = button.html();

                button.prop('disabled', true);
                button.html('🔄 Ανανέωση...');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'wsl_refresh_statistics',
                        nonce: '<?php echo wp_create_nonce('wsl_refresh_statistics'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            // Εμφάνιση μηνύματος επιτυχίας
                            var notice = $('<div class="notice notice-success is-dismissible" style="margin: 10px 0;"><p>✅ ' + response.data.message + '</p></div>');
                            $('.wrap h1').after(notice);

                            // Auto-dismiss μετά από 3 δευτερόλεπτα
                            setTimeout(function() {
                                notice.fadeOut(500, function() {
                                    notice.remove();
                                });
                            }, 3000);

                            // Reload της σελίδας για να δείξει τα νέα δεδομένα
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            var errorNotice = $('<div class="notice notice-error is-dismissible" style="margin: 10px 0;"><p>❌ Σφάλμα ανανέωσης στατιστικών</p></div>');
                            $('.wrap h1').after(errorNotice);

                            setTimeout(function() {
                                errorNotice.fadeOut(500, function() {
                                    errorNotice.remove();
                                });
                            }, 5000);
                        }

                        button.prop('disabled', false);
                        button.html(originalText);
                    },
                    error: function() {
                        var networkError = $('<div class="notice notice-error is-dismissible" style="margin: 10px 0;"><p>🌐 Σφάλμα δικτύου κατά την ανανέωση</p></div>');
                        $('.wrap h1').after(networkError);

                        setTimeout(function() {
                            networkError.fadeOut(500, function() {
                                networkError.remove();
                            });
                        }, 5000);

                        button.prop('disabled', false);
                        button.html(originalText);
                    }
                });
            });

            // Ενημέρωση κάθε 100ms για ακρίβεια
            if ($('#wsl-countdown-timer').length) {
                updateCountdown();
                setInterval(updateCountdown, 100);
            }

            if ($('#wsl-countdown-timer-logs').length) {
                updateCountdownLogs();
                setInterval(updateCountdownLogs, 100);
            }

            // Accordion functionality με fallback
            function initAccordion() {
                $('.wsl-accordion-header').off('click.accordion').on('click.accordion', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    var $header = $(this);
                    var $content = $header.next('.wsl-accordion-content');
                    var $toggle = $header.find('.wsl-accordion-toggle');

                    if ($content.is(':visible')) {
                        $content.slideUp(200, function() {
                            $content.hide();
                        });
                        $toggle.text('▼');
                    } else {
                        $content.slideDown(200, function() {
                            $content.show();
                        });
                        $toggle.text('▲');
                    }
                });
            }

            // Αρχικοποίηση accordion
            initAccordion();

            // Fallback με vanilla JS αν το jQuery έχει πρόβλημα
            document.addEventListener('DOMContentLoaded', function() {
                var headers = document.querySelectorAll('.wsl-accordion-header');
                headers.forEach(function(header) {
                    header.addEventListener('click', function(e) {
                        e.preventDefault();
                        var content = this.nextElementSibling;
                        var toggle = this.querySelector('.wsl-accordion-toggle');

                        if (content.style.display === 'block') {
                            content.style.display = 'none';
                            toggle.textContent = '▼';
                        } else {
                            content.style.display = 'block';
                            toggle.textContent = '▲';
                        }
                    });
                });
            });

            // Φίλτρα logs - με έλεγχο ύπαρξης στοιχείων
            if ($('#wsl-filter-form').length) {
                $('#wsl-filter-form').on('submit', function(e) {
                    e.preventDefault();
                    filterLogs();
                });
            }

            if ($('#wsl-search-input').length) {
                $('#wsl-search-input').on('input', function() {
                    var searchTerm = $(this).val();
                    if (searchTerm.length >= 2 || searchTerm.length === 0) {
                        searchLogs(searchTerm);
                    }
                });
            }

            if ($('#wsl-reset-filters').length) {
                $('#wsl-reset-filters').on('click', function() {
                    if ($('#wsl-filter-form').length) {
                        $('#wsl-filter-form')[0].reset();
                    }
                    if ($('#wsl-search-input').length) {
                        $('#wsl-search-input').val('');
                    }
                    $('.wsl-log-row').show();
                });
            }

            function filterLogs() {
                try {
                    var filters = {
                        date_from: $('#filter-date-from').length ? $('#filter-date-from').val() : '',
                        date_to: $('#filter-date-to').length ? $('#filter-date-to').val() : '',
                        username: $('#filter-username').length ? $('#filter-username').val() : '',
                        email: $('#filter-email').length ? $('#filter-email').val() : '',
                        role: $('#filter-role').length ? $('#filter-role').val() : '',
                        user_id: $('#filter-user-id').length ? $('#filter-user-id').val() : ''
                    };

                    $('.wsl-log-row, .wsl-user-row').each(function() {
                        var row = $(this);
                        var show = true;

                        // Έλεγχος κάθε φίλτρου με safe access
                        if (filters.date_from && row.data('date') && row.data('date') < filters.date_from) show = false;
                        if (filters.date_to && row.data('date') && row.data('date') > filters.date_to) show = false;
                        if (filters.username && row.data('username') && row.data('username').toString().toLowerCase().indexOf(filters.username.toLowerCase()) === -1) show = false;
                        if (filters.email && row.data('email') && row.data('email').toString().toLowerCase().indexOf(filters.email.toLowerCase()) === -1) show = false;
                        if (filters.role && row.data('role') && row.data('role').toString().toLowerCase().indexOf(filters.role.toLowerCase()) === -1) show = false;
                        if (filters.user_id && row.data('user-id') && row.data('user-id') != filters.user_id) show = false;

                        row.toggle(show);
                    });
                } catch (error) {
                    console.warn('Error in filterLogs:', error.message);
                }
            }

            // Protect User functionality
            $('.wsl-protect-user-btn').on('click', function() {
                var button = $(this);
                var userId = button.data('user-id');
                var username = button.data('username');

                // Άμεση προστασία χωρίς confirmation
                button.prop('disabled', true);
                button.html('⏳');

                // AJAX call για προστασία χρήστη
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'wsl_protect_user',
                        user_id: userId,
                        nonce: '<?php echo wp_create_nonce('wsl_protect_user'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            // Ενημέρωση του row με smooth animation
                            var row = button.closest('tr');

                            // Smooth transition για το background color
                            row.animate({opacity: 0.5}, 200, function() {
                                // Αλλαγή κλάσεων για χρώμα
                                row.removeClass('wsl-deleted-user wsl-warning-user wsl-safe-user');
                                row.addClass('wsl-protected-user');

                                // Ενημέρωση last login στήλης (7η στήλη - 0-indexed)
                                var lastLoginCell = row.find('td').eq(6);
                                lastLoginCell.html('<strong style="color: #0c5460;">' + response.data.last_login + '</strong>');

                                // Ενημέρωση κατάστασης (9η στήλη - 0-indexed)
                                var statusCell = row.find('td').eq(8);
                                statusCell.html('<span style="color: #0c5460; font-weight: bold;">🛡️ Προστατευμένος χρήστης</span>');

                                // Αλλαγή του button
                                button.html('✅');
                                button.prop('disabled', true);
                                button.attr('title', 'Χρήστης προστατευμένος');
                                button.css('color', '#0c5460');

                                // Fade back in με το νέο χρώμα
                                row.animate({opacity: 1}, 300);
                            });

                            // Compact success message
                            var notice = $('<div class="notice notice-success is-dismissible" style="margin: 10px 0;"><p>✅ <strong>' + username + '</strong> προστατεύθηκε!</p></div>');
                            $('.wrap h1').after(notice);

                            // Auto-dismiss μετά από 3 δευτερόλεπτα
                            setTimeout(function() {
                                notice.fadeOut(500, function() {
                                    notice.remove();
                                });
                            }, 3000);
                        } else {
                            // Error handling χωρίς alert
                            button.prop('disabled', false);
                            button.html('👤🛡️');

                            // Error message
                            var errorNotice = $('<div class="notice notice-error is-dismissible" style="margin: 10px 0;"><p>❌ Σφάλμα προστασίας χρήστη: ' + (response.data || 'Άγνωστο σφάλμα') + '</p></div>');
                            $('.wrap h1').after(errorNotice);

                            setTimeout(function() {
                                errorNotice.fadeOut(500, function() {
                                    errorNotice.remove();
                                });
                            }, 5000);
                        }
                    },
                    error: function(xhr, status, error) {
                        // Network error handling χωρίς alert
                        button.prop('disabled', false);
                        button.html('👤🛡️');

                        // Network error message
                        var networkError = $('<div class="notice notice-error is-dismissible" style="margin: 10px 0;"><p>🌐 Σφάλμα δικτύου. Παρακαλώ δοκιμάστε ξανά.</p></div>');
                        $('.wrap h1').after(networkError);

                        setTimeout(function() {
                            networkError.fadeOut(500, function() {
                                networkError.remove();
                            });
                        }, 5000);
                    }
                });
            });

            function searchLogs(searchTerm) {
                try {
                    if (!searchTerm) {
                        $('.wsl-log-row, .wsl-user-row').show();
                        return;
                    }

                    $('.wsl-log-row, .wsl-user-row').each(function() {
                        var row = $(this);
                        var text = row.text().toLowerCase();
                        row.toggle(text.indexOf(searchTerm.toLowerCase()) !== -1);
                    });
                } catch (error) {
                    console.warn('Error in searchLogs:', error.message);
                }
            }
        });
        </script>
        <?php
    }
});

function wsl_admin_page() {
    // Χειρισμός actions
    if (isset($_GET['action']) && wp_verify_nonce($_GET['_wpnonce'], 'wsl_action')) {
        switch ($_GET['action']) {
            case 'clear_logs':
                WSL_Logger::clear_logs();
                echo '<div class="notice notice-success"><p>Τα logs διαγράφηκαν επιτυχώς!</p></div>';
                break;

            case 'enhance_logs':
                $updated = WSL_Logger::enhance_old_logs();
                if ($updated) {
                    echo '<div class="notice notice-success"><p>Τα παλιά logs ενημερώθηκαν με βασικές πληροφορίες!</p></div>';
                } else {
                    echo '<div class="notice notice-info"><p>Δεν βρέθηκαν παλιά logs για ενημέρωση.</p></div>';
                }
                break;

            case 'create_sample':
                $count = WSL_Logger::create_sample_logs();
                echo '<div class="notice notice-success"><p>Δημιουργήθηκαν ' . $count . ' sample logs για testing!</p></div>';
                break;
        }
    }

    // Χειρισμός φίλτρων
    $filters = [];
    // Λήψη ανενεργών χρηστών αντί για logs
    if (isset($_GET['filter_submit'])) {
        $filters = [
            'date_from' => sanitize_text_field($_GET['date_from'] ?? ''),
            'date_to' => sanitize_text_field($_GET['date_to'] ?? ''),
            'username' => sanitize_text_field($_GET['username'] ?? ''),
            'email' => sanitize_text_field($_GET['email'] ?? ''),
            'role' => sanitize_text_field($_GET['role'] ?? ''),
            'user_id' => sanitize_text_field($_GET['user_id'] ?? ''),
        ];
        $inactive_users = wsl_get_filtered_inactive_users($filters);
    } else {
        $inactive_users = wsl_get_inactive_users();
    }

    $inactive_count = count($inactive_users);
    $current_tab = isset($_GET['tab']) ? $_GET['tab'] : 'logs';
    $export_urls = WSL_Export::get_export_buttons();
    $stats = WSL_Statistics::get_chart_data();
    ?>
    <div class="wrap">
        <h1>WSL Διαχείριση Ανενεργών Χρηστών</h1>

        <h2 class="nav-tab-wrapper">
            <a href="#logs" class="nav-tab <?php echo $current_tab === 'logs' ? 'nav-tab-active' : ''; ?>">
                👥 Ανενεργοί Χρήστες (<?php echo $inactive_count; ?>)
            </a>
            <a href="#statistics" class="nav-tab <?php echo $current_tab === 'statistics' ? 'nav-tab-active' : ''; ?>">
                📈 Στατιστικά
            </a>
            <a href="#settings" class="nav-tab <?php echo $current_tab === 'settings' ? 'nav-tab-active' : ''; ?>">
                ⚙️ Ρυθμίσεις
            </a>
        </h2>

        <!-- Tab: Logs -->
        <div id="logs" class="wsl-tab-content <?php echo $current_tab === 'logs' ? 'active' : ''; ?>">
            <!-- Accordion για Φίλτρα & Export -->
            <div class="wsl-accordion">
                <div class="wsl-accordion-header">
                    <span>🔍 Φίλτρα & Αναζήτηση</span>
                    <span class="wsl-accordion-toggle">▼</span>
                </div>
                <div class="wsl-accordion-content">
                    <!-- Φίλτρα Αναζήτησης -->
                    <form id="wsl-filter-form" method="get">
                        <input type="hidden" name="page" value="wsl-remove-inactive-users">
                        <input type="hidden" name="filter_submit" value="1">

                        <div class="wsl-filter-row">
                            <label>Αναζήτηση:</label>
                            <input type="text" id="wsl-search-input" placeholder="Αναζήτηση..." style="width: 200px;">

                            <label>Από:</label>
                            <input type="date" name="date_from" id="filter-date-from" value="<?php echo esc_attr($filters['date_from'] ?? ''); ?>">

                            <label>Έως:</label>
                            <input type="date" name="date_to" id="filter-date-to" value="<?php echo esc_attr($filters['date_to'] ?? ''); ?>">

                            <button type="submit" class="button button-primary">Φίλτρα</button>
                            <button type="button" id="wsl-reset-filters" class="button">Καθαρισμός</button>
                        </div>

                        <div class="wsl-filter-row">
                            <label>Username:</label>
                            <input type="text" name="username" id="filter-username" value="<?php echo esc_attr($filters['username'] ?? ''); ?>">

                            <label>Email:</label>
                            <input type="text" name="email" id="filter-email" value="<?php echo esc_attr($filters['email'] ?? ''); ?>">

                            <label>Ρόλος:</label>
                            <input type="text" name="role" id="filter-role" value="<?php echo esc_attr($filters['role'] ?? ''); ?>">

                            <label>User ID:</label>
                            <input type="number" name="user_id" id="filter-user-id" value="<?php echo esc_attr($filters['user_id'] ?? ''); ?>">
                        </div>
                    </form>

                    <hr style="margin: 15px 0;">

                    <!-- Export Buttons -->
                    <div class="wsl-export-buttons">
                        <h4 style="margin: 0 0 10px 0;">📊 Export Δεδομένων</h4>
                        <a href="<?php echo esc_url($export_urls['csv']); ?>" class="button">📄 Export CSV</a>
                        <a href="<?php echo esc_url($export_urls['excel']); ?>" class="button">📊 Export Excel</a>
                    </div>

                    <hr style="margin: 15px 0;">

                    <!-- Διαχείριση Logs -->
                    <div class="wsl-log-management">
                        <h4 style="margin: 0 0 10px 0;">🔧 Διαχείριση Logs</h4>
                        <a href="<?php echo wp_nonce_url(admin_url('users.php?page=wsl-remove-inactive-users&action=enhance_logs'), 'wsl_action'); ?>"
                           class="button button-secondary">🔄 Ενημέρωση Παλιών Logs</a>

                        <a href="<?php echo wp_nonce_url(admin_url('users.php?page=wsl-remove-inactive-users&action=create_sample'), 'wsl_action'); ?>"
                           class="button button-secondary">📝 Δημιουργία Sample Data</a>

                        <a href="<?php echo wp_nonce_url(admin_url('users.php?page=wsl-remove-inactive-users&action=clear_logs'), 'wsl_action'); ?>"
                           class="button button-link-delete wsl-clear-logs">🗑️ Διαγραφή Όλων</a>

                        <p class="description" style="margin-top: 8px;">
                            <strong>Ενημέρωση Παλιών Logs:</strong> Προσθέτει βασικές πληροφορίες σε παλιά logs που δεν έχουν username/email.<br>
                            <strong>Sample Data:</strong> Δημιουργεί 3 δείγματα logs για testing του interface.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Στατιστικά Σύνοψης -->
            <div class="wsl-stats">
                <div class="stat-item">
                    <div class="stat-number"><?php echo $inactive_count; ?></div>
                    <div>Ανενεργοί Χρήστες</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo count($inactive_users); ?></div>
                    <div>Εμφανιζόμενοι</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">
                        <?php
                        $frequency = get_option('wsl_check_frequency', 'hourly');
                        $frequency_labels = [
                            'hourly' => 'Ωριαία',
                            'every_2_hours' => '2ωρη',
                            'every_4_hours' => '4ωρη',
                            'twicedaily' => '2x/ημέρα',
                            'daily' => 'Ημερήσια',
                            'weekly' => 'Εβδομαδιαία'
                        ];
                        echo isset($frequency_labels[$frequency]) ? $frequency_labels[$frequency] : 'Ωριαία';
                        ?>
                    </div>
                    <div>Συχνότητα Ελέγχου</div>
                </div>
                <?php
                $checks_enabled = get_option('wsl_enable_checks', true);
                if ($checks_enabled):
                    $schedule_info = WSL_Scheduler::get_next_run_info();
                    $next_run = $schedule_info['next_run'];
                    $frequency = $schedule_info['frequency'];
                ?>
                <div class="stat-item">
                    <div class="stat-number wsl-countdown-timer" id="wsl-countdown-timer-logs">--:--:--:---</div>
                    <div>Επόμενος Έλεγχος</div>
                    <div id="wsl-next-run-timestamp-logs"
                         data-timestamp="<?php echo $next_run; ?>"
                         data-frequency="<?php echo $frequency; ?>"
                         data-next-scheduled="<?php echo wp_next_scheduled('wsl_check_inactive_users') ?: $next_run; ?>"
                         style="display:none;"></div>
                </div>
                <?php endif; ?>
                <div class="stat-item">
                    <div class="stat-number">
                        <?php
                        if ($checks_enabled && $next_run) {
                            echo 'Ενεργό';
                        } else {
                            echo 'Ανενεργό';
                        }
                        ?>
                    </div>
                    <div>Αυτόματος Έλεγχος</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">
                        <?php
                        $simulate_mode = get_option('wsl_simulate', true);
                        if ($simulate_mode) {
                            echo '<span style="color: orange;">Προσομοίωση</span>';
                        } else {
                            echo '<span style="color: red;">Πραγματικό</span>';
                        }
                        ?>
                    </div>
                    <div>Λειτουργία</div>
                </div>
            </div>

            <?php if (!empty($inactive_users)): ?>

                <table class="widefat fixed striped">
                    <thead>
                        <tr>
                            <th style="width: 60px;">User ID</th>
                            <th style="width: 120px;">Username</th>
                            <th style="width: 150px;">Όνομα</th>
                            <th style="width: 200px;">Email</th>
                            <th style="width: 100px;">Ρόλος</th>
                            <th style="width: 130px;">Ημ/νία Εγγραφής</th>
                            <th style="width: 130px;">Last Login</th>
                            <th style="width: 100px;">Protect User</th>
                            <th style="width: 150px;">Κατάσταση</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($inactive_users as $user):
                            // Υπολογισμός χρόνου μέχρι τη διαγραφή
                            $frequency = get_option('wsl_check_frequency', 'hourly');
                            $frequency_intervals = [
                                'hourly' => 3600,
                                'every_2_hours' => 7200,
                                'every_4_hours' => 14400,
                                'twicedaily' => 43200,
                                'daily' => 86400,
                                'weekly' => 604800
                            ];
                            $timeout = isset($frequency_intervals[$frequency]) ? $frequency_intervals[$frequency] : 3600;

                            $registered_time = strtotime($user['registered_date']);
                            $now = current_time('timestamp');
                            $time_since_registration = $now - $registered_time;
                            $time_until_deletion = max(0, $timeout - $time_since_registration);

                            // Έλεγχος αν ο χρήστης έχει last_login (προστατευμένος)
                            $user_last_login = get_user_meta($user['user_id'], 'last_login', true);
                            $is_protected = !empty($user_last_login);

                            // Χρωματική κωδικοποίηση βάσει κατάστασης
                            $row_class = '';
                            if ($is_protected) {
                                $row_class = 'wsl-protected-user'; // Μπλε - προστατευμένος χρήστης
                            } elseif ($time_until_deletion <= 0) {
                                $row_class = 'wsl-deleted-user'; // Κόκκινο - θα διαγραφεί στον επόμενο έλεγχο
                            } elseif ($time_until_deletion <= 3600) { // Λιγότερο από 1 ώρα
                                $row_class = 'wsl-warning-user'; // Κίτρινο - προειδοποίηση
                            } else {
                                $row_class = 'wsl-safe-user'; // Πράσινο - ασφαλής προς το παρόν
                            }

                            $user_date = date('Y-m-d', strtotime($user['registered_date']));
                        ?>
                        <tr class="wsl-user-row <?php echo $row_class; ?>"
                            data-user-id="<?php echo esc_attr($user['user_id']); ?>"
                            data-username="<?php echo esc_attr($user['username']); ?>"
                            data-email="<?php echo esc_attr($user['email']); ?>"
                            data-role="<?php echo esc_attr($user['roles']); ?>"
                            data-date="<?php echo esc_attr($user_date); ?>"
                            data-time-until-deletion="<?php echo $time_until_deletion; ?>">
                            <td>
                                <?php echo esc_html($user['user_id']); ?>
                                <span style="color: orange; font-weight: bold;" title="Ανενεργός χρήστης">⚠</span>
                            </td>
                            <td>
                                <strong><?php echo esc_html($user['username']); ?></strong>
                                <br><small style="color: #666;">Εγγεγραμμένος χρήστης</small>
                            </td>
                            <td>
                                <strong><?php echo esc_html($user['display_name']); ?></strong>
                                <br><small style="color: #666;">Δεν έχει συνδεθεί ποτέ</small>
                            </td>
                            <td>
                                <strong><?php echo esc_html($user['email']); ?></strong>
                            </td>
                            <td>
                                <?php echo esc_html($user['roles']); ?>
                            </td>
                            <td><?php echo date('d/m/Y H:i', strtotime($user['registered_date'])); ?></td>
                            <td>
                                <?php
                                // Λήψη last login από τη βάση δεδομένων
                                $last_login = get_user_meta($user['user_id'], 'last_login', true);
                                if (!empty($last_login)) {
                                    echo date('d/m/Y H:i', strtotime($last_login));
                                } else {
                                    echo '<span style="color: #999;">Ποτέ</span>';
                                }
                                ?>
                            </td>
                            <td style="text-align: center;">
                                <?php if ($is_protected): ?>
                                    <button type="button"
                                            class="wsl-protect-user-btn"
                                            disabled
                                            title="Χρήστης ήδη προστατευμένος"
                                            style="background: none; border: none; font-size: 18px; cursor: not-allowed; color: #0c5460;">
                                        ✅
                                    </button>
                                <?php else: ?>
                                    <button type="button"
                                            class="wsl-protect-user-btn"
                                            data-user-id="<?php echo esc_attr($user['user_id']); ?>"
                                            data-username="<?php echo esc_attr($user['username']); ?>"
                                            title="Προστασία χρήστη από διαγραφή"
                                            style="background: none; border: none; font-size: 18px; cursor: pointer; color: #0073aa;">
                                        👤🛡️
                                    </button>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                if ($is_protected) {
                                    echo '<span style="color: #0c5460; font-weight: bold;">🛡️ Προστατευμένος χρήστης</span>';
                                } elseif ($time_until_deletion <= 0) {
                                    echo '<span style="color: red; font-weight: bold;">🔴 Θα διαγραφεί στον επόμενο έλεγχο</span>';
                                } elseif ($time_until_deletion <= 3600) {
                                    $minutes = ceil($time_until_deletion / 60);
                                    echo '<span style="color: orange; font-weight: bold;">⚠️ Διαγραφή σε ' . $minutes . ' λεπτά</span>';
                                } else {
                                    $hours = floor($time_until_deletion / 3600);
                                    $minutes = floor(($time_until_deletion % 3600) / 60);
                                    if ($hours > 0) {
                                        echo '<span style="color: green;">✅ Ασφαλής για ' . $hours . 'ω ' . $minutes . 'λ</span>';
                                    } else {
                                        echo '<span style="color: green;">✅ Ασφαλής για ' . $minutes . ' λεπτά</span>';
                                    }
                                }
                                ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <p class="description">
                    <strong>Χρωματική Κωδικοποίηση Χρηστών:</strong><br>
                    <span style="background: #d1ecf1; padding: 2px 6px; border-radius: 3px; color: #0c5460;">🔵 Μπλε φόντο</span> = Προστατευμένος χρήστης (δεν θα διαγραφεί ποτέ) |
                    <span style="background: #d4edda; padding: 2px 6px; border-radius: 3px; color: #155724;">🟢 Πράσινο φόντο</span> = Ασφαλής (περισσότερο από 1 ώρα μέχρι τη διαγραφή) |
                    <span style="background: #fff3cd; padding: 2px 6px; border-radius: 3px; color: #856404;">🟡 Κίτρινο φόντο</span> = Προειδοποίηση (λιγότερο από 1 ώρα μέχρι τη διαγραφή) |
                    <span style="background: #f8d7da; padding: 2px 6px; border-radius: 3px; color: #721c24;">🔴 Κόκκινο φόντο</span> = Θα διαγραφεί στον επόμενο έλεγχο |
                    <span style="color: orange; font-weight: bold;">⚠</span> = Ανενεργός χρήστης |
                    <span style="color: #0073aa; font-weight: bold;">👤🛡️</span> = Κουμπί προστασίας
                </p>
            <?php else: ?>
                <p>🎉 Δεν υπάρχουν ανενεργοί χρήστες προς διαγραφή! Όλοι οι χρήστες έχουν συνδεθεί τουλάχιστον μία φορά.</p>
            <?php endif; ?>
        </div>

        <!-- Tab: Statistics -->
        <div id="statistics" class="wsl-tab-content <?php echo $current_tab === 'statistics' ? 'active' : ''; ?>">
            <h2>📈 Στατιστικά Διαγραφών</h2>

            <?php
            // Έλεγχος αν υπάρχουν δεδομένα
            $total_logs = WSL_Logger::get_logs_count();
            if ($total_logs === 0):
            ?>
            <div class="notice notice-info">
                <p>
                    <strong>ℹ️ Δεν υπάρχουν δεδομένα διαγραφών ακόμη.</strong><br>
                    Τα στατιστικά θα εμφανιστούν μετά την πρώτη διαγραφή χρηστών ή μπορείτε να δημιουργήσετε δεδομένα δοκιμής.
                </p>
                <p>
                    <a href="<?php echo admin_url('users.php?page=wsl-remove-inactive-users&tab=statistics&create_sample_data=1'); ?>"
                       class="button button-secondary">
                        🧪 Δημιουργία Δεδομένων Δοκιμής
                    </a>
                </p>
            </div>
            <?php endif; ?>

            <?php
            // Δημιουργία sample data αν ζητήθηκε
            if (isset($_GET['create_sample_data']) && current_user_can('manage_options')) {
                $created_count = WSL_Logger::create_sample_logs();
                echo '<div class="notice notice-success"><p>✅ Δημιουργήθηκαν ' . $created_count . ' δεδομένα δοκιμής!</p></div>';
                // Ανανέωση των stats μετά τη δημιουργία sample data
                $stats = WSL_Statistics::get_chart_data();
                $total_logs = WSL_Logger::get_logs_count();
            }
            ?>

            <?php if ($total_logs > 0): ?>
            <!-- Εργαλεία Διαχείρισης Δεδομένων -->
            <div style="margin-bottom: 20px;">
                <button type="button" id="wsl-refresh-stats" class="button button-primary">
                    🔄 Ανανέωση Στατιστικών
                </button>
                <a href="<?php echo admin_url('users.php?page=wsl-remove-inactive-users&tab=statistics&create_sample_data=1'); ?>"
                   class="button button-secondary">
                    🧪 Προσθήκη Δεδομένων Δοκιμής
                </a>
                <a href="<?php echo admin_url('users.php?page=wsl-remove-inactive-users&tab=logs&clear_logs=1'); ?>"
                   class="button button-secondary"
                   onclick="return confirm('Είστε σίγουροι ότι θέλετε να διαγράψετε όλα τα logs;');">
                    🗑️ Εκκαθάριση Όλων των Logs
                </a>
            </div>

            <!-- Στατιστικά Σύνοψης -->
            <div class="wsl-summary-stats">
                <?php foreach ($stats['summary'] as $key => $value): ?>
                <div class="wsl-summary-stat">
                    <div class="number">
                        <?php
                        switch($key) {
                            case 'total': echo $value; break;
                            case 'today': echo $value; break;
                            case 'this_week': echo $value; break;
                            case 'this_month': echo $value; break;
                            case 'avg_per_day': echo $value; break;
                            case 'first_deletion': echo $value ? date('d/m/Y', strtotime($value)) : 'Καμία'; break;
                            case 'last_deletion': echo $value ? date('d/m/Y', strtotime($value)) : 'Καμία'; break;
                        }
                        ?>
                    </div>
                    <div class="label">
                        <?php
                        switch($key) {
                            case 'total': echo 'Συνολικές Διαγραφές'; break;
                            case 'today': echo 'Σήμερα'; break;
                            case 'this_week': echo 'Αυτή την Εβδομάδα'; break;
                            case 'this_month': echo 'Αυτόν τον Μήνα'; break;
                            case 'avg_per_day': echo 'Μέσος Όρος/Ημέρα'; break;
                            case 'first_deletion': echo 'Πρώτη Διαγραφή'; break;
                            case 'last_deletion': echo 'Τελευταία Διαγραφή'; break;
                        }
                        ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Γραφήματα -->
            <div class="wsl-chart-grid">
                <div class="wsl-chart-container">
                    <h3>Διαγραφές ανά Ημέρα (Τελευταίες 30 ημέρες)</h3>
                    <canvas id="dailyChart" width="400" height="200"></canvas>
                </div>

                <div class="wsl-chart-container">
                    <h3>Διαγραφές ανά Ρόλο</h3>
                    <canvas id="rolesChart" width="400" height="200"></canvas>
                </div>
            </div>

            <div class="wsl-chart-grid">
                <div class="wsl-chart-container">
                    <h3>Διαγραφές ανά Μήνα (Τελευταίοι 12 μήνες)</h3>
                    <canvas id="monthlyChart" width="400" height="200"></canvas>
                </div>

                <div class="wsl-chart-container">
                    <h3>Διαγραφές ανά Ώρα</h3>
                    <canvas id="hourlyChart" width="400" height="200"></canvas>
                </div>
            </div>

            <script>
            // Περιμένουμε να φορτώσει το Chart.js με multiple retries
            document.addEventListener('DOMContentLoaded', function() {
                var retryCount = 0;
                var maxRetries = 10;

                function tryInitializeCharts() {
                    if (typeof Chart !== 'undefined') {
                        initializeCharts();
                        return;
                    }

                    retryCount++;
                    if (retryCount < maxRetries) {
                        console.warn('Chart.js δεν έχει φορτώσει ακόμη. Προσπάθεια ' + retryCount + '/' + maxRetries);
                        setTimeout(tryInitializeCharts, 500);
                    } else {
                        console.warn('Chart.js δεν φόρτωσε μετά από ' + maxRetries + ' προσπάθειες. Τα γραφήματα δεν θα εμφανιστούν.');
                        // Εμφάνιση μηνύματος στον χρήστη
                        var chartsContainer = document.querySelector('.wsl-chart-grid');
                        if (chartsContainer) {
                            chartsContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #d63638;"><p>⚠️ Τα γραφήματα δεν μπόρεσαν να φορτώσουν.</p><p><small>Ελέγξτε τη σύνδεσή σας στο διαδίκτυο και ανανεώστε τη σελίδα.</small></p></div>';
                        }
                    }
                }

                tryInitializeCharts();
            });

            function initializeCharts() {
                // Έλεγχος αν το Chart.js είναι διαθέσιμο
                if (typeof Chart === 'undefined') {
                    console.warn('Chart.js δεν είναι διαθέσιμο. Τα γραφήματα δεν θα εμφανιστούν.');
                    // Εμφάνιση μηνύματος στον χρήστη
                    var chartsContainer = document.querySelector('.wsl-chart-grid');
                    if (chartsContainer) {
                        chartsContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;"><p>📊 Τα γραφήματα δεν είναι διαθέσιμα αυτή τη στιγμή.</p><p><small>Παρακαλώ ανανεώστε τη σελίδα.</small></p></div>';
                    }
                    return;
                }

                try {
                    // Δεδομένα για γραφήματα
                    const chartData = <?php echo json_encode($stats); ?>;

                    // Έλεγχος αν υπάρχουν δεδομένα
                    const hasData = chartData && chartData.summary && chartData.summary.total > 0;

                    // Γράφημα ημερήσιων διαγραφών
                    if (hasData) {
                        const dailyCtx = document.getElementById('dailyChart');
                        if (dailyCtx) {
                            new Chart(dailyCtx.getContext('2d'), {
                        type: 'line',
                        data: {
                            labels: Object.keys(chartData.daily),
                            datasets: [{
                                label: 'Διαγραφές',
                                data: Object.values(chartData.daily),
                                borderColor: '#0073aa',
                                backgroundColor: 'rgba(0, 115, 170, 0.1)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        stepSize: 1
                                    }
                                }
                            }
                        }
                        });
                    }
                }

                // Γράφημα ρόλων
                if (hasData) {
                    const rolesCtx = document.getElementById('rolesChart');
                    if (rolesCtx) {
                        new Chart(rolesCtx.getContext('2d'), {
                        type: 'doughnut',
                        data: {
                            labels: Object.keys(chartData.roles),
                            datasets: [{
                                data: Object.values(chartData.roles),
                                backgroundColor: [
                                    '#0073aa', '#005177', '#00a0d2', '#72aee6',
                                    '#2271b1', '#135e96', '#043959', '#1d2327'
                                ]
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                        });
                    }
                }

                // Γράφημα μηνιαίων διαγραφών
                if (hasData) {
                    const monthlyCtx = document.getElementById('monthlyChart');
                    if (monthlyCtx) {
                        new Chart(monthlyCtx.getContext('2d'), {
                        type: 'bar',
                        data: {
                            labels: Object.keys(chartData.monthly),
                            datasets: [{
                                label: 'Διαγραφές',
                                data: Object.values(chartData.monthly),
                                backgroundColor: '#0073aa'
                            }]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        stepSize: 1
                                    }
                                }
                            }
                        }
                        });
                    }
                }

                // Γράφημα ωριαίων διαγραφών
                if (hasData) {
                    const hourlyCtx = document.getElementById('hourlyChart');
                    if (hourlyCtx) {
                        new Chart(hourlyCtx.getContext('2d'), {
                        type: 'bar',
                        data: {
                            labels: Object.keys(chartData.hourly),
                            datasets: [{
                                label: 'Διαγραφές',
                                data: Object.values(chartData.hourly),
                                backgroundColor: '#72aee6'
                            }]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        stepSize: 1
                                    }
                                }
                            }
                        }
                        });
                    }
                }
                } catch (error) {
                    console.warn('WSL Plugin: Error initializing charts:', error.message);
                    // Εμφάνιση μηνύματος σφάλματος στον χρήστη
                    var chartsContainer = document.querySelector('.wsl-chart-grid');
                    if (chartsContainer) {
                        chartsContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #d63638;"><p>⚠️ Σφάλμα φόρτωσης γραφημάτων.</p><p><small>Παρακαλώ ανανεώστε τη σελίδα.</small></p></div>';
                    }
                }
            }
            </script>
            <?php endif; ?>
        </div>

        <!-- Tab: Settings -->
        <div id="settings" class="wsl-tab-content <?php echo $current_tab === 'settings' ? 'active' : ''; ?>">
            <form method="post" action="options.php">
                <?php
                settings_fields('wsl_settings_group');
                do_settings_sections('wsl-settings');
                submit_button('Αποθήκευση Ρυθμίσεων');
                ?>
            </form>

            <hr>

            <h3>🔧 Εργαλεία Διαχείρισης</h3>
            <p class="description">
                <strong>Αρχικοποίηση Last Login Fields:</strong> Προσθέτει last_login field σε παλιούς χρήστες για προστασία από διαγραφή.
            </p>
            <p>
                <a href="<?php echo admin_url('users.php?page=wsl-remove-inactive-users&tab=settings&wsl_init_last_login=1'); ?>"
                   class="button button-secondary"
                   onclick="return confirm('Είστε σίγουροι ότι θέλετε να αρχικοποιήσετε τα last_login fields για όλους τους παλιούς χρήστες;');">
                    🛠️ Αρχικοποίηση Last Login Fields
                </a>
            </p>

            <hr>

            <h3>Πληροφορίες Plugin</h3>
            <table class="form-table">
                <tr>
                    <th scope="row">Κατάσταση Ελέγχων</th>
                    <td>
                        <?php
                        $checks_enabled = get_option('wsl_enable_checks', true);
                        if ($checks_enabled) {
                            echo '<span style="color: green; font-weight: bold;">✓ Ενεργοποιημένοι</span>';
                        } else {
                            echo '<span style="color: red; font-weight: bold;">✗ Απενεργοποιημένοι</span>';
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Επόμενος Έλεγχος</th>
                    <td>
                        <?php
                        $schedule_info = WSL_Scheduler::get_next_run_info();
                        $next_run = $schedule_info['next_run'];
                        $frequency = $schedule_info['frequency'];
                        $interval_seconds = $schedule_info['interval_seconds'];
                        $checks_enabled = get_option('wsl_enable_checks', true);

                        if (!$checks_enabled) {
                            echo '<em>Οι έλεγχοι είναι απενεργοποιημένοι</em>';
                        } elseif ($next_run) {
                            echo date('d/m/Y H:i:s', $next_run);

                            // Εμφάνιση συχνότητας
                            $frequency_labels = [
                                'hourly' => 'κάθε ώρα',
                                'every_2_hours' => 'κάθε 2 ώρες',
                                'every_4_hours' => 'κάθε 4 ώρες',
                                'twicedaily' => 'δύο φορές την ημέρα',
                                'daily' => 'καθημερινά',
                                'weekly' => 'εβδομαδιαία'
                            ];
                            $frequency_label = isset($frequency_labels[$frequency]) ? $frequency_labels[$frequency] : $frequency;
                            echo ' <em>(' . $frequency_label . ')</em>';

                            // Εμφάνιση διαστήματος διαγραφής
                            $hours = floor($interval_seconds / 3600);
                            $minutes = floor(($interval_seconds % 3600) / 60);
                            echo '<br><small style="color: #666;">Διαγραφή χρηστών που δεν συνδέονται εντός ';
                            if ($hours >= 24) {
                                $days = floor($hours / 24);
                                echo $days . ' ημέρ' . ($days == 1 ? 'ας' : 'ών');
                            } elseif ($hours > 0) {
                                echo $hours . ' ώρ' . ($hours == 1 ? 'ας' : 'ών');
                                if ($minutes > 0) echo ' και ' . $minutes . ' λεπτών';
                            } else {
                                echo $minutes . ' λεπτών';
                            }
                            echo '</small>';
                        } else {
                            echo 'Δεν είναι προγραμματισμένος';
                        }
                        ?>
                    </td>
                </tr>
                <?php if ($next_run && $checks_enabled): ?>
                <tr>
                    <th scope="row">Χρόνος μέχρι τον Έλεγχο</th>
                    <td>
                        <div class="wsl-countdown-timer" id="wsl-countdown-timer">--:--:--:---</div>
                        <div id="wsl-next-run-timestamp"
                             data-timestamp="<?php echo $next_run; ?>"
                             data-frequency="<?php echo $frequency; ?>"
                             data-interval="<?php echo $interval_seconds; ?>"
                             data-next-scheduled="<?php echo wp_next_scheduled('wsl_check_inactive_users') ?: $next_run; ?>"
                             style="display:none;"></div>
                    </td>
                </tr>
                <?php endif; ?>
                <tr>
                    <th scope="row">Τελευταία Εκτέλεση</th>
                    <td>
                        <?php
                        $last_run = get_option('wsl_last_run_time', false);
                        if ($last_run) {
                            echo date('d/m/Y H:i:s', $last_run);
                            $deleted_count = get_option('wsl_last_run_deleted', 0);
                            if ($deleted_count === 0) {
                                $checks_enabled_at_runtime = get_option('wsl_enable_checks', true);
                                if (!$checks_enabled_at_runtime) {
                                    echo ' <em>(έλεγχοι απενεργοποιημένοι)</em>';
                                } else {
                                    echo ' <em>(δεν διαγράφηκαν χρήστες)</em>';
                                }
                            } else {
                                echo ' <em>(διαγράφηκαν ' . $deleted_count . ' χρήστες)</em>';
                            }
                        } else {
                            echo 'Δεν έχει εκτελεστεί ακόμη';
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Συνολικοί Χρήστες</th>
                    <td><?php echo count_users()['total_users']; ?></td>
                </tr>
                <tr>
                    <th scope="row">Λογική Διαγραφής</th>
                    <td>
                        <?php
                        $frequency = get_option('wsl_check_frequency', 'hourly');
                        $frequency_labels = [
                            'hourly' => 'Διαγραφή χρηστών που δεν συνδέονται εντός 1 ώρας',
                            'every_2_hours' => 'Διαγραφή χρηστών που δεν συνδέονται εντός 2 ωρών',
                            'every_4_hours' => 'Διαγραφή χρηστών που δεν συνδέονται εντός 4 ωρών',
                            'twicedaily' => 'Διαγραφή χρηστών που δεν συνδέονται εντός 12 ωρών',
                            'daily' => 'Διαγραφή χρηστών που δεν συνδέονται εντός 24 ωρών',
                            'weekly' => 'Διαγραφή χρηστών που δεν συνδέονται εντός 7 ημερών'
                        ];
                        echo isset($frequency_labels[$frequency]) ? $frequency_labels[$frequency] : 'Διαγραφή χρηστών που δεν συνδέονται εντός 1 ώρας';
                        ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Έκδοση Plugin</th>
                    <td>2.0 (Pro Edition με Έξυπνη Διαγραφή)</td>
                </tr>
            </table>
        </div>
    </div>
    <?php
}

/**
 * AJAX handler για προστασία χρήστη
 */
function wsl_ajax_protect_user() {
    // Έλεγχος nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wsl_protect_user')) {
        wp_die('Security check failed');
    }

    // Έλεγχος permissions
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    $user_id = intval($_POST['user_id']);

    if (!$user_id) {
        wp_send_json_error('Invalid user ID');
    }

    // Έλεγχος αν ο χρήστης υπάρχει
    $user = get_userdata($user_id);
    if (!$user) {
        wp_send_json_error('User not found');
    }

    // Ενημέρωση του last_login με την τρέχουσα ημερομηνία (mysql format)
    $current_time = current_time('mysql');
    $updated = update_user_meta($user_id, 'last_login', $current_time);

    if ($updated !== false) {
        // Επιτυχής ενημέρωση
        wp_send_json_success([
            'message' => 'User protected successfully',
            'last_login' => date('d/m/Y H:i', strtotime($current_time)),
            'user_id' => $user_id,
            'username' => $user->user_login
        ]);
    } else {
        wp_send_json_error('Failed to update user meta');
    }
}

// Προσθήκη AJAX hooks
add_action('wp_ajax_wsl_protect_user', 'wsl_ajax_protect_user');
add_action('wp_ajax_wsl_get_next_scheduled', 'wsl_ajax_get_next_scheduled');
add_action('wp_ajax_wsl_refresh_statistics', 'wsl_ajax_refresh_statistics');

/**
 * Manual initialization για last_login fields (για debugging)
 */
function wsl_manual_init_last_login() {
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    global $wpdb;

    // Λήψη όλων των χρηστών που δεν έχουν last_login field
    $users_without_last_login = $wpdb->get_results("
        SELECT u.ID, u.user_login, u.user_registered
        FROM {$wpdb->users} u
        LEFT JOIN {$wpdb->usermeta} um ON (u.ID = um.user_id AND um.meta_key = 'last_login')
        WHERE um.meta_value IS NULL
    ");

    $current_timestamp = current_time('timestamp');
    $protected_count = 0;

    foreach ($users_without_last_login as $user) {
        // Για παλιούς χρήστες (εγγραφή πριν από 24 ώρες), βάζουμε current timestamp
        $user_registered_timestamp = strtotime($user->user_registered);
        $hours_since_registration = ($current_timestamp - $user_registered_timestamp) / 3600;

        if ($hours_since_registration > 24) {
            // Παλιός χρήστης - προστασία με current timestamp
            update_user_meta($user->ID, 'last_login', date('Y-m-d H:i:s', $current_timestamp));
            $protected_count++;
        }
    }

    echo '<div class="notice notice-success"><p>';
    echo "✅ Προστατεύθηκαν {$protected_count} παλιοί χρήστες από τους " . count($users_without_last_login) . " χρήστες χωρίς last_login field.";
    echo '</p></div>';
}

// Hook για manual initialization (μόνο για admins)
if (isset($_GET['wsl_init_last_login']) && current_user_can('manage_options')) {
    add_action('admin_init', 'wsl_manual_init_last_login');
}

/**
 * AJAX handler για λήψη επόμενου προγραμματισμένου ελέγχου
 */
function wsl_ajax_get_next_scheduled() {
    // Έλεγχος nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wsl_get_next_scheduled')) {
        wp_die('Security check failed');
    }

    // Έλεγχος permissions
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    // Λήψη επόμενου προγραμματισμένου ελέγχου
    $next_scheduled = wp_next_scheduled('wsl_check_inactive_users');

    if ($next_scheduled) {
        wp_send_json_success([
            'next_scheduled' => $next_scheduled,
            'next_scheduled_formatted' => date('d/m/Y H:i:s', $next_scheduled),
            'current_time' => current_time('timestamp'),
            'time_until' => $next_scheduled - current_time('timestamp')
        ]);
    } else {
        wp_send_json_error('No scheduled check found');
    }
}

/**
 * AJAX handler για refresh στατιστικών
 */
function wsl_ajax_refresh_statistics() {
    // Έλεγχος nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wsl_refresh_statistics')) {
        wp_die('Security check failed');
    }

    // Έλεγχος permissions
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    // Λήψη ανανεωμένων στατιστικών
    $stats = WSL_Statistics::get_chart_data();
    $total_logs = WSL_Logger::get_logs_count();

    wp_send_json_success([
        'stats' => $stats,
        'total_logs' => $total_logs,
        'message' => 'Στατιστικά ενημερώθηκαν επιτυχώς'
    ]);
}
