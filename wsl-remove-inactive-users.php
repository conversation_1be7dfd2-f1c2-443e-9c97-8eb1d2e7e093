<?php
/**
 * Plugin Name: WSL Remove Inactive Users
 * Description: Αφαιρεί χρήστες που εγγράφονται αλλά δεν συνδέονται ποτέ εντός καθορισμένου χρόνου. Βελτιωμένη έκδοση με ασφάλεια και καλύτερο UI.
 * Version: 2.0
 * Author: WSL TakisM
 * License: GPL2
 * Text Domain: wsl-remove-inactive-users
 */

defined('ABSPATH') or die('No script kiddies please!');

// Φόρτωση των κλάσεων
require_once plugin_dir_path(__FILE__) . 'includes/class-scheduler.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-cleaner.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-logger.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-settings.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-export.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-statistics.php';
require_once plugin_dir_path(__FILE__) . 'includes/admin-ui.php';

// Hooks ενεργοποίησης και απενεργοποίησης
register_activation_hook(__FILE__, 'wsl_plugin_activation');
register_deactivation_hook(__FILE__, ['WSL_Scheduler', 'deactivate']);

/**
 * Εκτελείται κατά την ενεργοποίηση του plugin
 */
function wsl_plugin_activation() {
    // Προσθήκη last_login field σε όλους τους υπάρχοντες χρήστες
    wsl_initialize_last_login_fields();

    // Δημιουργία default ρυθμίσεων αν δεν υπάρχουν
    wsl_set_default_options();

    // Κλήση του αρχικού activation hook του scheduler
    WSL_Scheduler::activate();
}

/**
 * Προσθέτει last_login field σε όλους τους χρήστες
 */
function wsl_initialize_last_login_fields() {
    global $wpdb;

    // Λήψη όλων των χρηστών που δεν έχουν last_login field
    $users_without_last_login = $wpdb->get_results("
        SELECT u.ID, u.user_registered
        FROM {$wpdb->users} u
        LEFT JOIN {$wpdb->usermeta} um ON (u.ID = um.user_id AND um.meta_key = 'last_login')
        WHERE um.meta_value IS NULL
    ");

    $current_timestamp = current_time('timestamp');

    foreach ($users_without_last_login as $user) {
        // Για νέους χρήστες (εγγραφή τελευταίες 24 ώρες), δεν βάζουμε last_login
        // Για παλιούς χρήστες, βάζουμε current timestamp για να τους προστατεύσουμε
        $user_registered_timestamp = strtotime($user->user_registered);
        $hours_since_registration = ($current_timestamp - $user_registered_timestamp) / 3600;

        if ($hours_since_registration > 24) {
            // Παλιός χρήστης - προστασία με current timestamp
            update_user_meta($user->ID, 'last_login', date('Y-m-d H:i:s', $current_timestamp));
        }
        // Νέοι χρήστες δεν παίρνουν last_login - θα ελεγχθούν κανονικά
    }

    // Log της διαδικασίας
    error_log('WSL Plugin: Initialized last_login fields for ' . count($users_without_last_login) . ' users');
}

/**
 * Δημιουργία default ρυθμίσεων
 */
function wsl_set_default_options() {
    // Προσθήκη default ρυθμίσεων μόνο αν δεν υπάρχουν
    if (get_option('wsl_enable_checks') === false) {
        update_option('wsl_enable_checks', true);
    }

    if (get_option('wsl_simulate') === false) {
        update_option('wsl_simulate', true); // Default σε safe mode
    }

    if (get_option('wsl_exclude_roles') === false) {
        update_option('wsl_exclude_roles', ['administrator']);
    }

    if (get_option('wsl_check_frequency') === false) {
        update_option('wsl_check_frequency', 'hourly');
    }

    if (get_option('wsl_notify_admin') === false) {
        update_option('wsl_notify_admin', false);
    }
}

// Hook για καταγραφή τελευταίας σύνδεσης
add_action('wp_login', function($user_login, $user) {
    update_user_meta($user->ID, 'last_login', current_time('mysql'));
}, 10, 2);

// Προσθήκη custom cron intervals
add_filter('cron_schedules', function($schedules) {
    $schedules['every_2_hours'] = [
        'interval' => 7200, // 2 ώρες σε δευτερόλεπτα
        'display' => 'Κάθε 2 ώρες'
    ];

    $schedules['every_4_hours'] = [
        'interval' => 14400, // 4 ώρες σε δευτερόλεπτα
        'display' => 'Κάθε 4 ώρες'
    ];

    return $schedules;
});

// Hook για ενημέρωση του schedule όταν αλλάζει η συχνότητα
add_action('update_option_wsl_check_frequency', function($old_value, $new_value) {
    if ($old_value !== $new_value) {
        WSL_Scheduler::update_schedule();
    }
}, 10, 2);

// Hook για ενεργοποίηση/απενεργοποίηση ελέγχων
add_action('update_option_wsl_enable_checks', function($old_value, $new_value) {
    if ($old_value !== $new_value) {
        if ($new_value) {
            // Ενεργοποίηση ελέγχων
            WSL_Scheduler::update_schedule();
        } else {
            // Απενεργοποίηση ελέγχων
            wp_clear_scheduled_hook('wsl_check_inactive_users');
        }
    }
}, 10, 2);