<?php
class WSL_Export {
    
    public static function init() {
        add_action('admin_init', [self::class, 'handle_export']);
    }
    
    public static function handle_export() {
        if (!isset($_GET['wsl_export']) || !wp_verify_nonce($_GET['_wpnonce'], 'wsl_export')) {
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_die('Δεν έχετε δικαίωμα για αυτή την ενέργεια.');
        }
        
        $format = sanitize_text_field($_GET['wsl_export']);
        $logs = WSL_Logger::get_logs();
        
        if (empty($logs)) {
            wp_die('Δεν υπάρχουν logs για export.');
        }
        
        switch ($format) {
            case 'csv':
                self::export_csv($logs);
                break;
            case 'excel':
                self::export_excel($logs);
                break;
            default:
                wp_die('Μη έγκυρος τύπος export.');
        }
    }
    
    private static function export_csv($logs) {
        $filename = 'wsl-removed-users-' . date('Y-m-d-H-i-s') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);
        header('Pragma: no-cache');
        header('Expires: 0');
        
        $output = fopen('php://output', 'w');
        
        // UTF-8 BOM για σωστή εμφάνιση ελληνικών στο Excel
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Headers
        fputcsv($output, [
            'User ID',
            'Username', 
            'Όνομα',
            'Email',
            'Ρόλος',
            'Ημ/νία Εγγραφής',
            'Ημ/νία Διαγραφής',
            'Τελευταία Σύνδεση'
        ]);
        
        // Data
        foreach ($logs as $log) {
            fputcsv($output, [
                $log['user_id'],
                $log['username'] ?? 'Μη διαθέσιμο',
                $log['display_name'] ?? 'Μη διαθέσιμο',
                $log['email'] ?? 'Μη διαθέσιμο',
                $log['roles'] ?? 'Μη διαθέσιμο',
                $log['registered_date'] ?? 'Μη διαθέσιμο',
                $log['timestamp'],
                $log['last_login'] ?: 'Ποτέ'
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    private static function export_excel($logs) {
        $filename = 'wsl-removed-users-' . date('Y-m-d-H-i-s') . '.xls';
        
        header('Content-Type: application/vnd.ms-excel; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);
        header('Pragma: no-cache');
        header('Expires: 0');
        
        echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
        echo '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="ProgId" content="Excel.Sheet"></head>';
        echo '<body>';
        echo '<table border="1">';
        
        // Headers
        echo '<tr style="background-color: #f1f1f1; font-weight: bold;">';
        echo '<td>User ID</td>';
        echo '<td>Username</td>';
        echo '<td>Όνομα</td>';
        echo '<td>Email</td>';
        echo '<td>Ρόλος</td>';
        echo '<td>Ημ/νία Εγγραφής</td>';
        echo '<td>Ημ/νία Διαγραφής</td>';
        echo '<td>Τελευταία Σύνδεση</td>';
        echo '</tr>';
        
        // Data
        foreach ($logs as $log) {
            echo '<tr>';
            echo '<td>' . esc_html($log['user_id']) . '</td>';
            echo '<td>' . esc_html($log['username'] ?? 'Μη διαθέσιμο') . '</td>';
            echo '<td>' . esc_html($log['display_name'] ?? 'Μη διαθέσιμο') . '</td>';
            echo '<td>' . esc_html($log['email'] ?? 'Μη διαθέσιμο') . '</td>';
            echo '<td>' . esc_html($log['roles'] ?? 'Μη διαθέσιμο') . '</td>';
            echo '<td>' . esc_html($log['registered_date'] ?? 'Μη διαθέσιμο') . '</td>';
            echo '<td>' . esc_html($log['timestamp']) . '</td>';
            echo '<td>' . esc_html($log['last_login'] ?: 'Ποτέ') . '</td>';
            echo '</tr>';
        }
        
        echo '</table>';
        echo '</body>';
        echo '</html>';
        exit;
    }
    
    public static function get_export_buttons() {
        $csv_url = wp_nonce_url(
            admin_url('users.php?page=wsl-remove-inactive-users&wsl_export=csv'),
            'wsl_export'
        );
        
        $excel_url = wp_nonce_url(
            admin_url('users.php?page=wsl-remove-inactive-users&wsl_export=excel'),
            'wsl_export'
        );
        
        return [
            'csv' => $csv_url,
            'excel' => $excel_url
        ];
    }
}

WSL_Export::init();
