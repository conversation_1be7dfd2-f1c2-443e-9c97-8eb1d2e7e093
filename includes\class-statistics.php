<?php
class WSL_Statistics {
    
    public static function get_daily_stats($days = 30) {
        $logs = get_option('wsl_removed_users', []);
        $stats = [];
        
        // Δημιουργία array για τις τελευταίες X ημέρες
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i days"));
            $stats[$date] = 0;
        }
        
        // Μέτρηση διαγραφών ανά ημέρα
        foreach ($logs as $log) {
            $log_date = date('Y-m-d', strtotime($log['timestamp']));
            if (isset($stats[$log_date])) {
                $stats[$log_date]++;
            }
        }
        
        return $stats;
    }
    
    public static function get_monthly_stats($months = 12) {
        $logs = get_option('wsl_removed_users', []);
        $stats = [];
        
        // Δημιουργία array για τους τελευταίους X μήνες
        for ($i = $months - 1; $i >= 0; $i--) {
            $date = date('Y-m', strtotime("-$i months"));
            $stats[$date] = 0;
        }
        
        // Μέτρηση διαγραφών ανά μήνα
        foreach ($logs as $log) {
            $log_month = date('Y-m', strtotime($log['timestamp']));
            if (isset($stats[$log_month])) {
                $stats[$log_month]++;
            }
        }
        
        return $stats;
    }
    
    public static function get_role_stats() {
        $logs = get_option('wsl_removed_users', []);
        $stats = [];

        // Μετάφραση ρόλων
        $role_translations = [
            'administrator' => 'Διαχειριστής',
            'editor' => 'Συντάκτης',
            'author' => 'Συγγραφέας',
            'contributor' => 'Συνεισφέρων',
            'subscriber' => 'Συνδρομητής',
            'customer' => 'Πελάτης',
            'shop_manager' => 'Διαχειριστής Καταστήματος',
            'translator' => 'Μεταφραστής',
            'wholesaler' => 'Χονδρέμπορος'
        ];

        foreach ($logs as $log) {
            if (isset($log['roles']) && !empty($log['roles'])) {
                $roles = explode(', ', $log['roles']);
                foreach ($roles as $role) {
                    $role = trim($role);
                    // Μετάφραση του ρόλου αν υπάρχει
                    $translated_role = isset($role_translations[$role]) ? $role_translations[$role] : $role;

                    if (!isset($stats[$translated_role])) {
                        $stats[$translated_role] = 0;
                    }
                    $stats[$translated_role]++;
                }
            } else {
                if (!isset($stats['Μη διαθέσιμο'])) {
                    $stats['Μη διαθέσιμο'] = 0;
                }
                $stats['Μη διαθέσιμο']++;
            }
        }

        arsort($stats);
        return $stats;
    }
    
    public static function get_hourly_stats() {
        $logs = get_option('wsl_removed_users', []);
        $stats = [];
        
        // Αρχικοποίηση όλων των ωρών
        for ($i = 0; $i < 24; $i++) {
            $stats[sprintf('%02d:00', $i)] = 0;
        }
        
        foreach ($logs as $log) {
            $hour = date('H:00', strtotime($log['timestamp']));
            if (isset($stats[$hour])) {
                $stats[$hour]++;
            }
        }
        
        return $stats;
    }
    
    public static function get_summary_stats() {
        $logs = get_option('wsl_removed_users', []);
        $total = count($logs);
        
        if ($total === 0) {
            return [
                'total' => 0,
                'today' => 0,
                'this_week' => 0,
                'this_month' => 0,
                'avg_per_day' => 0,
                'first_deletion' => null,
                'last_deletion' => null
            ];
        }
        
        $today = date('Y-m-d');
        $week_start = date('Y-m-d', strtotime('monday this week'));
        $month_start = date('Y-m-01');
        
        $today_count = 0;
        $week_count = 0;
        $month_count = 0;
        $dates = [];
        
        foreach ($logs as $log) {
            $log_date = date('Y-m-d', strtotime($log['timestamp']));
            $dates[] = $log_date;
            
            if ($log_date === $today) {
                $today_count++;
            }
            
            if ($log_date >= $week_start) {
                $week_count++;
            }
            
            if ($log_date >= $month_start) {
                $month_count++;
            }
        }
        
        sort($dates);
        $first_date = reset($dates);
        $last_date = end($dates);
        
        // Υπολογισμός μέσου όρου ανά ημέρα
        $days_diff = max(1, (strtotime($last_date) - strtotime($first_date)) / (24 * 60 * 60) + 1);
        $avg_per_day = round($total / $days_diff, 2);
        
        return [
            'total' => $total,
            'today' => $today_count,
            'this_week' => $week_count,
            'this_month' => $month_count,
            'avg_per_day' => $avg_per_day,
            'first_deletion' => $first_date,
            'last_deletion' => $last_date
        ];
    }
    
    public static function get_chart_data() {
        return [
            'daily' => self::get_daily_stats(30),
            'monthly' => self::get_monthly_stats(12),
            'roles' => self::get_role_stats(),
            'hourly' => self::get_hourly_stats(),
            'summary' => self::get_summary_stats()
        ];
    }
}
