<?php
class WSL_Logger {
    public static function log_removal($user_id, $username = '', $display_name = '', $email = '', $roles = [], $registered_date = '') {
        $logs = get_option('wsl_removed_users', []);

        // Περιορισμό<PERSON> logs σε 1000 εγγραφές για απόδοση
        if (count($logs) >= 1000) {
            $logs = array_slice($logs, -900); // Κρατάμε τις τελευταίες 900
        }

        // Διασφάλιση ότι έχουμε τουλάχιστον βασικές πληροφορίες
        $username = !empty($username) ? $username : 'Άγνωστος';
        $display_name = !empty($display_name) ? $display_name : 'Μη διαθέσιμο';
        $email = !empty($email) ? $email : 'Μη διαθέσιμο';
        $roles_string = '';

        if (is_array($roles) && !empty($roles)) {
            $roles_string = implode(', ', $roles);
        } elseif (!empty($roles)) {
            $roles_string = $roles;
        } else {
            $roles_string = 'Μη διαθέσιμο';
        }

        $logs[] = [
            'user_id' => $user_id,
            'username' => $username,
            'display_name' => $display_name,
            'email' => $email,
            'roles' => $roles_string,
            'registered_date' => $registered_date,
            'last_login' => '', // Θα είναι πάντα κενό για ανενεργούς χρήστες
            'timestamp' => current_time('mysql'),
            'version' => '1.1', // Για να ξεχωρίζουμε τα νέα logs
            'type' => 'deletion'
        ];
        update_option('wsl_removed_users', $logs);
    }

    public static function log_simulation($user_id, $username = '', $display_name = '', $email = '', $roles = [], $registered_date = '') {
        $logs = get_option('wsl_simulation_logs', []);

        // Περιορισμός logs σε 1000 εγγραφές για απόδοση
        if (count($logs) >= 1000) {
            $logs = array_slice($logs, -900); // Κρατάμε τις τελευταίες 900
        }

        // Διασφάλιση ότι έχουμε τουλάχιστον βασικές πληροφορίες
        $username = !empty($username) ? $username : 'Άγνωστος';
        $display_name = !empty($display_name) ? $display_name : 'Μη διαθέσιμο';
        $email = !empty($email) ? $email : 'Μη διαθέσιμο';
        $roles_string = '';

        if (is_array($roles) && !empty($roles)) {
            $roles_string = implode(', ', $roles);
        } elseif (!empty($roles)) {
            $roles_string = $roles;
        } else {
            $roles_string = 'Μη διαθέσιμο';
        }

        $logs[] = [
            'user_id' => $user_id,
            'username' => $username,
            'display_name' => $display_name,
            'email' => $email,
            'roles' => $roles_string,
            'registered_date' => $registered_date,
            'last_login' => '', // Θα είναι πάντα κενό για ανενεργούς χρήστες
            'timestamp' => current_time('mysql'),
            'version' => '1.1',
            'type' => 'simulation'
        ];
        update_option('wsl_simulation_logs', $logs);
    }

    public static function get_logs() {
        $logs = get_option('wsl_removed_users', []);

        // Βελτίωση παλιών logs με στοιχεία από τη βάση αν υπάρχουν
        foreach ($logs as &$log) {
            // Αν είναι παλιό log χωρίς στοιχεία, προσπάθησε να τα βρεις
            if ((!isset($log['username']) || empty($log['username']) || $log['username'] === 'Άγνωστος') && isset($log['user_id'])) {
                $enhanced_data = self::try_get_user_data_from_db($log['user_id']);
                if ($enhanced_data) {
                    $log = array_merge($log, $enhanced_data);
                    $log['enhanced'] = true; // Σημαία ότι βελτιώθηκε
                }
            }
        }

        // Επιστροφή σε αντίστροφη σειρά (νεότερα πρώτα)
        return array_reverse($logs);
    }

    /**
     * Προσπαθεί να ανακτήσει στοιχεία χρήστη από τη βάση δεδομένων
     * Χρησιμοποιεί WordPress tables για να βρει πληροφορίες
     */
    private static function try_get_user_data_from_db($user_id) {
        global $wpdb;

        // Προσπάθησε να βρεις στοιχεία από τα WordPress logs ή άλλες πηγές
        // Αυτό μπορεί να μην δουλέψει πάντα γιατί ο χρήστης έχει διαγραφεί

        // Έλεγχος αν υπάρχουν στοιχεία στα comments (αν ο χρήστης είχε σχόλια)
        $comment_data = $wpdb->get_row($wpdb->prepare(
            "SELECT comment_author, comment_author_email
             FROM {$wpdb->comments}
             WHERE user_id = %d
             LIMIT 1",
            $user_id
        ));

        if ($comment_data) {
            return [
                'username' => $comment_data->comment_author ?: 'Άγνωστος',
                'display_name' => $comment_data->comment_author ?: 'Μη διαθέσιμο',
                'email' => $comment_data->comment_author_email ?: 'Μη διαθέσιμο',
                'roles' => 'Μη διαθέσιμο (από comments)',
                'registered_date' => 'Μη διαθέσιμο'
            ];
        }

        // Έλεγχος αν υπάρχουν στοιχεία στα posts (αν ο χρήστης είχε posts)
        $post_data = $wpdb->get_row($wpdb->prepare(
            "SELECT post_author, post_date
             FROM {$wpdb->posts}
             WHERE post_author = %d
             AND post_status != 'trash'
             LIMIT 1",
            $user_id
        ));

        if ($post_data) {
            // Προσπάθησε να βρεις το display name από post meta ή άλλες πηγές
            return [
                'username' => 'user_' . $user_id,
                'display_name' => 'Χρήστης #' . $user_id,
                'email' => 'Μη διαθέσιμο',
                'roles' => 'Μη διαθέσιμο (είχε posts)',
                'registered_date' => $post_data->post_date
            ];
        }

        // Τελευταία προσπάθεια - έλεγχος για activity logs αν υπάρχουν
        $activity_data = $wpdb->get_var($wpdb->prepare(
            "SELECT meta_value
             FROM {$wpdb->usermeta}
             WHERE user_id = %d
             AND meta_key = 'last_activity'
             LIMIT 1",
            $user_id
        ));

        if ($activity_data) {
            return [
                'username' => 'user_' . $user_id,
                'display_name' => 'Χρήστης #' . $user_id,
                'email' => 'Μη διαθέσιμο',
                'roles' => 'Μη διαθέσιμο',
                'registered_date' => 'Μη διαθέσιμο'
            ];
        }

        return null; // Δεν βρέθηκαν στοιχεία
    }

    public static function clear_logs() {
        delete_option('wsl_removed_users');
    }

    public static function get_logs_count() {
        $logs = get_option('wsl_removed_users', []);
        return count($logs);
    }

    public static function get_filtered_logs($filters = []) {
        $logs = self::get_logs();

        if (empty($filters)) {
            return $logs;
        }

        $filtered = [];

        foreach ($logs as $log) {
            $include = true;

            // Φίλτρο ημερομηνίας από
            if (!empty($filters['date_from'])) {
                $log_date = date('Y-m-d', strtotime($log['timestamp']));
                if ($log_date < $filters['date_from']) {
                    $include = false;
                }
            }

            // Φίλτρο ημερομηνίας έως
            if (!empty($filters['date_to'])) {
                $log_date = date('Y-m-d', strtotime($log['timestamp']));
                if ($log_date > $filters['date_to']) {
                    $include = false;
                }
            }

            // Φίλτρο username
            if (!empty($filters['username'])) {
                $username = $log['username'] ?? '';
                if (stripos($username, $filters['username']) === false) {
                    $include = false;
                }
            }

            // Φίλτρο email
            if (!empty($filters['email'])) {
                $email = $log['email'] ?? '';
                if (stripos($email, $filters['email']) === false) {
                    $include = false;
                }
            }

            // Φίλτρο ρόλου
            if (!empty($filters['role'])) {
                $roles = $log['roles'] ?? '';
                if (stripos($roles, $filters['role']) === false) {
                    $include = false;
                }
            }

            // Φίλτρο User ID
            if (!empty($filters['user_id'])) {
                if ($log['user_id'] != $filters['user_id']) {
                    $include = false;
                }
            }

            if ($include) {
                $filtered[] = $log;
            }
        }

        return $filtered;
    }

    public static function search_logs($search_term) {
        $logs = self::get_logs();

        if (empty($search_term)) {
            return $logs;
        }

        $filtered = [];
        $search_term = strtolower($search_term);

        foreach ($logs as $log) {
            $searchable_text = strtolower(implode(' ', [
                $log['user_id'],
                $log['username'] ?? '',
                $log['display_name'] ?? '',
                $log['email'] ?? '',
                $log['roles'] ?? '',
                $log['timestamp']
            ]));

            if (strpos($searchable_text, $search_term) !== false) {
                $filtered[] = $log;
            }
        }

        return $filtered;
    }

    /**
     * Ενημερώνει παλιά logs με βασικές πληροφορίες
     * Χρησιμοποιείται για να γεμίσει κενά στοιχεία
     */
    public static function enhance_old_logs() {
        $logs = get_option('wsl_removed_users', []);
        $updated = false;

        foreach ($logs as &$log) {
            // Αν είναι παλιό log χωρίς στοιχεία
            if (!isset($log['username']) || empty($log['username']) || $log['username'] === 'Άγνωστος') {
                $user_id = $log['user_id'];

                // Προσθήκη βασικών στοιχείων
                $log['username'] = 'user_' . $user_id;
                $log['display_name'] = 'Διαγραμμένος Χρήστης #' . $user_id;
                $log['email'] = 'deleted_user_' . $user_id . '@removed.local';
                $log['roles'] = 'subscriber'; // Πιθανότατα ήταν subscriber
                $log['registered_date'] = $log['timestamp']; // Χρήση timestamp ως εκτίμηση
                $log['last_login'] = ''; // Ήταν ανενεργός
                $log['version'] = '1.1'; // Σημαία ότι ενημερώθηκε
                $log['enhanced'] = true; // Σημαία ότι είναι enhanced

                $updated = true;
            }
        }

        if ($updated) {
            update_option('wsl_removed_users', $logs);
        }

        return $updated;
    }

    /**
     * Δημιουργεί mock data για testing
     */
    public static function create_sample_logs() {
        $sample_logs = [
            [
                'user_id' => 999,
                'username' => 'test_user_1',
                'display_name' => 'Test User One',
                'email' => '<EMAIL>',
                'roles' => 'subscriber',
                'registered_date' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'last_login' => '',
                'timestamp' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'version' => '1.1'
            ],
            [
                'user_id' => 998,
                'username' => 'test_user_2',
                'display_name' => 'Test User Two',
                'email' => '<EMAIL>',
                'roles' => 'contributor',
                'registered_date' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'last_login' => '',
                'timestamp' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                'version' => '1.1'
            ],
            [
                'user_id' => 997,
                'username' => 'inactive_user',
                'display_name' => 'Inactive User',
                'email' => '<EMAIL>',
                'roles' => 'subscriber',
                'registered_date' => date('Y-m-d H:i:s', strtotime('-5 days')),
                'last_login' => '',
                'timestamp' => date('Y-m-d H:i:s', strtotime('-30 minutes')),
                'version' => '1.1'
            ]
        ];

        $existing_logs = get_option('wsl_removed_users', []);
        $all_logs = array_merge($existing_logs, $sample_logs);
        update_option('wsl_removed_users', $all_logs);

        return count($sample_logs);
    }

    /**
     * Ελέγχει αν ένας χρήστης υπάρχει ακόμη στη βάση δεδομένων
     */
    public static function user_exists($user_id) {
        $user = get_user_by('id', $user_id);
        return $user !== false;
    }

    /**
     * Επιστρέφει πληροφορίες χρήστη αν υπάρχει
     */
    public static function get_current_user_info($user_id) {
        $user = get_user_by('id', $user_id);
        if ($user) {
            return [
                'exists' => true,
                'username' => $user->user_login,
                'display_name' => $user->display_name,
                'email' => $user->user_email,
                'roles' => implode(', ', $user->roles),
                'registered' => $user->user_registered
            ];
        }
        return ['exists' => false];
    }
}
